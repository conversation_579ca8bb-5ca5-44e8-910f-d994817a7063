import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  withDelay,
  interpolate,
  Easing
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export default function CompletionAnimation() {
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  const checkScale = useSharedValue(0);
  const textOpacity = useSharedValue(0);
  const rocketTranslateY = useSharedValue(0);
  const rocketOpacity = useSharedValue(0);

  useEffect(() => {
    // Ana animasyon sekansı
    const startAnimation = () => {
      // 1. <PERSON><PERSON>ve bardağı büyüme
      scale.value = withTiming(1, { 
        duration: 600, 
        easing: Easing.out(Easing.back(1.7)) 
      });
      opacity.value = withTiming(1, { duration: 400 });

      // 2. Check mark animasyonu
      setTimeout(() => {
        checkScale.value = withSequence(
          withTiming(1.2, { duration: 300 }),
          withTiming(1, { duration: 200 })
        );
      }, 800);

      // 3. Metin animasyonu
      setTimeout(() => {
        textOpacity.value = withTiming(1, { duration: 500 });
      }, 1200);

      // 4. Roket animasyonu
      setTimeout(() => {
        rocketOpacity.value = withTiming(1, { duration: 300 });
        rocketTranslateY.value = withTiming(-SCREEN_HEIGHT, { 
          duration: 1000,
          easing: Easing.out(Easing.cubic)
        });
      }, 1600);
    };

    startAnimation();
  }, []);

  const containerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value
  }));

  const checkStyle = useAnimatedStyle(() => ({
    transform: [{ scale: checkScale.value }]
  }));

  const textStyle = useAnimatedStyle(() => ({
    opacity: textOpacity.value
  }));

  const rocketStyle = useAnimatedStyle(() => ({
    opacity: rocketOpacity.value,
    transform: [{ translateY: rocketTranslateY.value }]
  }));

  return (
    <View style={styles.container}>
      {/* Arka plan gradient efekti */}
      <View style={styles.backgroundGradient} />
      
      {/* Ana animasyon container */}
      <Animated.View style={[styles.animationContainer, containerStyle]}>
        {/* Kahve bardağı ikonu */}
        <View style={styles.cupIcon}>
          <Ionicons name="cafe" size={80} color={Colors.primary} />
        </View>
        
        {/* Check mark overlay */}
        <Animated.View style={[styles.checkContainer, checkStyle]}>
          <View style={styles.checkBackground}>
            <Ionicons name="checkmark" size={40} color="white" />
          </View>
        </Animated.View>
      </Animated.View>

      {/* Başarı metni */}
      <Animated.View style={[styles.textContainer, textStyle]}>
        <Text style={[styles.successTitle, globalStyles.heading]}>
          Harika! 🎉
        </Text>
        <Text style={[styles.successSubtitle, globalStyles.body]}>
          Kahve yolculuğunuz başlıyor
        </Text>
      </Animated.View>

      {/* Roket animasyonu */}
      <Animated.View style={[styles.rocketContainer, rocketStyle]}>
        <Ionicons name="rocket" size={60} color={Colors.primary} />
      </Animated.View>

      {/* Parçacık efektleri */}
      <View style={styles.particlesContainer}>
        {[...Array(8)].map((_, index) => (
          <ParticleEffect key={index} delay={index * 100} />
        ))}
      </View>
    </View>
  );
}

// Parçacık efekti komponenti
function ParticleEffect({ delay = 0 }) {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0);

  useEffect(() => {
    setTimeout(() => {
      const randomX = (Math.random() - 0.5) * 200;
      const randomY = (Math.random() - 0.5) * 200;
      
      translateX.value = withTiming(randomX, { duration: 1500 });
      translateY.value = withTiming(randomY, { duration: 1500 });
      opacity.value = withSequence(
        withTiming(1, { duration: 300 }),
        withTiming(0, { duration: 1200 })
      );
      scale.value = withSequence(
        withTiming(1, { duration: 300 }),
        withTiming(0, { duration: 1200 })
      );
    }, delay);
  }, [delay]);

  const particleStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value }
    ],
    opacity: opacity.value
  }));

  return (
    <Animated.View style={[styles.particle, particleStyle]}>
      <Ionicons name="sparkles" size={16} color={Colors.primary} />
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative'
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.background,
    opacity: 0.9
  },
  animationContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative'
  },
  cupIcon: {
    padding: 20
  },
  checkContainer: {
    position: 'absolute',
    bottom: 10,
    right: 10
  },
  checkBackground: {
    backgroundColor: '#4CAF50',
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5
  },
  textContainer: {
    alignItems: 'center',
    marginTop: 40
  },
  successTitle: {
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 8
  },
  successSubtitle: {
    color: Colors.tabIconDefault,
    textAlign: 'center'
  },
  rocketContainer: {
    position: 'absolute',
    bottom: SCREEN_HEIGHT / 2
  },
  particlesContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center'
  },
  particle: {
    position: 'absolute'
  }
});
