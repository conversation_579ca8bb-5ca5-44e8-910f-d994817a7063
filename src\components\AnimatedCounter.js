import React, { useEffect, useState } from 'react';
import { Text } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';

export default function AnimatedCounter({
  value,
  duration = 1500,
  style,
  delay = 0
}) {
  const animatedValue = useSharedValue(0);
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => {
      animatedValue.value = withTiming(value, {
        duration,
      }, (finished) => {
        if (finished) {
          // Update display value when animation finishes
          setDisplayValue(value);
        }
      });
    }, delay);

    return () => clearTimeout(timer);
  }, [value, duration, delay]);

  // Update display value during animation
  useEffect(() => {
    const interval = setInterval(() => {
      if (animatedValue.value < value) {
        setDisplayValue(Math.floor(animatedValue.value));
      }
    }, 50);

    return () => clearInterval(interval);
  }, [animatedValue.value, value]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(animatedValue.value > 0 ? 1 : 0, { duration: 300 }),
      transform: [
        {
          scale: withTiming(animatedValue.value > 0 ? 1 : 0.8, { duration: 300 }),
        },
      ],
    };
  });

  return (
    <Animated.Text style={[style, animatedStyle]}>
      {displayValue}
    </Animated.Text>
  );
}
