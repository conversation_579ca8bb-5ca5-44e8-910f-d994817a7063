import React, { useEffect } from 'react';
import { Text } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  useDerivedValue,
  runOnJS,
} from 'react-native-reanimated';

const AnimatedText = Animated.createAnimatedComponent(Text);

export default function AnimatedCounter({ 
  value, 
  duration = 1500, 
  style,
  delay = 0 
}) {
  const animatedValue = useSharedValue(0);
  const displayValue = useSharedValue(0);

  useEffect(() => {
    const timer = setTimeout(() => {
      animatedValue.value = withTiming(value, { duration });
    }, delay);

    return () => clearTimeout(timer);
  }, [value, duration, delay]);

  // Update display value on JS thread
  useDerivedValue(() => {
    runOnJS((val) => {
      displayValue.value = Math.floor(val);
    })(animatedValue.value);
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(animatedValue.value > 0 ? 1 : 0, { duration: 300 }),
      transform: [
        {
          scale: withTiming(animatedValue.value > 0 ? 1 : 0.8, { duration: 300 }),
        },
      ],
    };
  });

  return (
    <AnimatedText style={[style, animatedStyle]}>
      {Math.floor(animatedValue.value)}
    </AnimatedText>
  );
}
