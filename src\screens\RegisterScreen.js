import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { supabase } from '../lib/supabase';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';

export default function RegisterScreen({ navigation }) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [loading, setLoading] = useState(false);

  const getRegisterErrorMessage = (error) => {
    switch (error.message) {
      case 'User already registered':
        return 'Bu e-posta adresi zaten kayıtlı. Giriş yapmayı deneyin.';
      case 'Password should be at least 6 characters':
        return 'Şifre en az 6 karakter olmalıdır.';
      case 'Invalid email':
        return 'Ge<PERSON><PERSON><PERSON> bir e-posta adresi girin.';
      case 'Signup is disabled':
        return 'Yeni kayıtlar şu anda kapalı. Lütfen daha sonra tekrar deneyin.';
      default:
        return `Kayıt hatası: ${error.message}`;
    }
  };

  const validateInputs = () => {
    if (!email || !password || !fullName) {
      Alert.alert('Eksik Bilgi', 'Lütfen tüm alanları doldurun.');
      return false;
    }

    if (!email.includes('@')) {
      Alert.alert('Geçersiz E-posta', 'Lütfen geçerli bir e-posta adresi girin.');
      return false;
    }

    if (password.length < 6) {
      Alert.alert('Zayıf Şifre', 'Şifre en az 6 karakter olmalıdır.');
      return false;
    }

    if (fullName.trim().length < 2) {
      Alert.alert('Geçersiz İsim', 'Lütfen geçerli bir ad soyad girin.');
      return false;
    }

    return true;
  };

  const handleRegister = async () => {
    if (!validateInputs()) {
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signUp({
        email: email.toLowerCase().trim(),
        password,
        options: {
          data: {
            full_name: fullName.trim(),
          }
        }
      });

      if (error) {
        Alert.alert('Kayıt Başarısız', getRegisterErrorMessage(error));
      } else if (data.user) {
        Alert.alert(
          'Kayıt Başarılı!',
          'Hesabınız oluşturuldu. E-posta adresinize gönderilen doğrulama linkine tıklayın, ardından giriş yapabilirsiniz.',
          [{
            text: 'Giriş Sayfasına Git',
            onPress: () => navigation.navigate('Login')
          }]
        );
      }
    } catch (error) {
      console.error('Unexpected register error:', error);
      Alert.alert('Beklenmeyen Hata', 'Bir hata oluştu. Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={[styles.title, globalStyles.heading]}>Hesap Oluştur</Text>
        <Text style={[styles.subtitle, globalStyles.body]}>
          Kahve keşif yolculuğuna başlamak için hesabınızı oluşturun
        </Text>

        <View style={styles.form}>
          <TextInput
            style={[styles.input, globalStyles.body]}
            placeholder="Ad Soyad"
            placeholderTextColor={Colors.tabIconDefault}
            value={fullName}
            onChangeText={setFullName}
            autoCapitalize="words"
          />

          <TextInput
            style={[styles.input, globalStyles.body]}
            placeholder="E-posta"
            placeholderTextColor={Colors.tabIconDefault}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
          />

          <TextInput
            style={[styles.input, globalStyles.body]}
            placeholder="Şifre"
            placeholderTextColor={Colors.tabIconDefault}
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            autoCapitalize="none"
          />

          <TouchableOpacity 
            style={[styles.button, loading && styles.buttonDisabled]}
            onPress={handleRegister}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color={Colors.text} />
            ) : (
              <Text style={[styles.buttonText, globalStyles.body]}>Hesap Oluştur</Text>
            )}
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.linkButton}
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={[styles.linkText, globalStyles.bodySmall]}>
              Zaten hesabınız var mı? Giriş yapın
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  title: {
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    color: Colors.tabIconDefault,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 24,
  },
  form: {
    gap: 16,
  },
  input: {
    backgroundColor: Colors.card,
    color: Colors.text,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  button: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: Colors.text,
    fontWeight: '600',
  },
  linkButton: {
    paddingVertical: 12,
    alignItems: 'center',
  },
  linkText: {
    color: Colors.primary,
  },
});