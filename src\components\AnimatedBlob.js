import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { Colors } from '../constants/Colors';

export default function AnimatedBlob({ size = 200, color = Colors.primary }) {
  const animationValue = useSharedValue(0);

  useEffect(() => {
    animationValue.value = withRepeat(
      withTiming(1, { duration: 4000 }),
      -1,
      true
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    const scale = interpolate(animationValue.value, [0, 1], [1, 1.1]);
    const rotate = interpolate(animationValue.value, [0, 1], [0, 10]);
    const borderRadius = interpolate(animationValue.value, [0, 1], [size * 0.3, size * 0.5]);

    return {
      transform: [
        { scale },
        { rotate: `${rotate}deg` },
      ],
      borderRadius,
    };
  });

  return (
    <Animated.View
      style={[
        styles.blob,
        {
          width: size,
          height: size,
          backgroundColor: color,
        },
        animatedStyle,
      ]}
    />
  );
}

const styles = StyleSheet.create({
  blob: {
    position: 'absolute',
    opacity: 0.1,
    zIndex: -1,
  },
});
