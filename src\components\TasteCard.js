import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, { 
  FadeInDown, 
  useAnimatedStyle, 
  withSpring,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import spacingScale from '../constants/spacing';

const getIconForTaste = (taste) => {
  const icons = {
    'Meyvemsi': 'leaf-outline',
    'Çikolatalı': 'cafe-outline',
    'Fındıklı': 'nutrition-outline',
    'Baharatlı': 'flame-outline',
    'Çiçeksi': 'flower-outline',
    'Çalışmak': 'laptop-outline',
    'Sosyalleşmek': 'people-outline',
    'Dinlenmek': 'bed-outline',
    'Okumak': 'book-outline',
    'Toplantı': 'business-outline',
  };
  return icons[taste] || 'cafe-outline';
};

export default function TasteCard({ 
  taste, 
  index = 0,
  type = 'preference' // 'preference' or 'purpose'
}) {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  return (
    <Animated.View
      entering={FadeInDown.delay(index * 100).springify()}
      style={styles.wrapper}
    >
      <Animated.View
        style={[styles.container, animatedStyle]}
        onTouchStart={handlePressIn}
        onTouchEnd={handlePressOut}
      >
        <View style={styles.iconContainer}>
          <Ionicons
            name={getIconForTaste(taste)}
            size={16}
            color={Colors.primary}
          />
        </View>
        <Text style={[styles.text, globalStyles.bodySmall]}>
          {taste}
        </Text>
      </Animated.View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    // Wrapper for layout animation - no conflicting properties
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 20,
    paddingHorizontal: spacingScale.md,
    paddingVertical: spacingScale.sm,
    marginRight: spacingScale.sm,
    marginBottom: spacingScale.sm,
    shadowColor: Colors.primary,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
    borderWidth: 1,
    borderColor: Colors.primary + '20',
  },
  iconContainer: {
    marginRight: spacingScale.sm,
  },
  text: {
    color: Colors.text,
    fontWeight: '500',
  },
});
