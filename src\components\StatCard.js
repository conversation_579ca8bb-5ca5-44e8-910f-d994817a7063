import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import spacingScale from '../constants/spacing';
import AnimatedCounter from './AnimatedCounter';

export default function StatCard({ 
  icon, 
  value, 
  label, 
  index = 0,
  delay = 0 
}) {
  return (
    <Animated.View 
      entering={FadeInUp.delay(delay)}
      style={styles.container}
    >
      <View style={styles.iconContainer}>
        <Ionicons name={icon} size={24} color={Colors.primary} />
      </View>
      <View style={styles.content}>
        <AnimatedCounter 
          value={value} 
          style={[styles.value, globalStyles.subheading]}
          delay={delay + 300}
        />
        <Text style={[styles.label, globalStyles.bodySmall]}>
          {label}
        </Text>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: spacingScale.md,
    marginHorizontal: spacingScale.sm,
    marginBottom: spacingScale.md,
    shadowColor: Colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
    borderWidth: 1,
    borderColor: Colors.primary + '10',
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacingScale.md,
  },
  content: {
    flex: 1,
  },
  value: {
    color: Colors.text,
    fontWeight: 'bold',
  },
  label: {
    color: Colors.tabIconDefault,
    marginTop: spacingScale.xs,
  },
});
