import React, { useState, useEffect, useRef } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  ActivityIndicator, 
  Alert,
  Dimensions
} from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withSpring,
  withSequence,
  interpolate,
  interpolateColor,
  runOnJS,
  Extrapolate
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import AnimatedCoffeeCup from '../components/AnimatedCoffeeCup';
import CompletionAnimation from '../components/CompletionAnimation';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const COFFEE_PREFERENCES = [
  'Meyvemsi', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>ındıkl<PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Ko<PERSON> Kavrum', '<PERSON><PERSON>ık Kavrum'
];

const USAGE_PURPOSES = [
  { id: 1, label: 'Yeni yerler keşfetmek', icon: '🗺️' },
  { id: 2, label: 'Çalışma mekanı bulmak', icon: '💻' },
  { id: 3, label: 'Sosyalleşmek', icon: '👥' },
  { id: 4, label: 'Kahve bilgimi artırmak', icon: '📚' },
  { id: 5, label: 'Fotoğraf çekmek', icon: '📸' },
  { id: 6, label: 'Rahatlamak', icon: '☕' }
];

export default function OnboardingScreen() {
  const [currentStep, setCurrentStep] = useState(0);
  const [username, setUsername] = useState('');
  const [selectedPreferences, setSelectedPreferences] = useState([]);
  const [selectedPurposeIds, setSelectedPurposeIds] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showCompletion, setShowCompletion] = useState(false);
  
  const scrollViewRef = useRef(null);

  // Navigation functions
  const goToNextStep = () => {
    if (currentStep < 2) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({
          x: nextStep * SCREEN_WIDTH,
          animated: true
        });
      }
    }
  };

  const goToPrevStep = () => {
    if (currentStep > 0) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({
          x: prevStep * SCREEN_WIDTH,
          animated: true
        });
      }
    }
  };

  const togglePreference = (preference) => {
    setSelectedPreferences(prev => 
      prev.includes(preference) 
        ? prev.filter(p => p !== preference)
        : [...prev, preference]
    );
  };

  const togglePurpose = (purposeId) => {
    setSelectedPurposeIds(prev => 
      prev.includes(purposeId) 
        ? prev.filter(id => id !== purposeId)
        : [...prev, purposeId]
    );
  };

  const handleNext = () => {
    if (currentStep === 0 && username.trim()) {
      goToNextStep();
    } else if (currentStep === 1 && selectedPreferences.length > 0) {
      goToNextStep();
    }
  };

  const validateOnboardingData = () => {
    if (!username.trim()) {
      Alert.alert('Eksik Bilgi', 'Lütfen bir kullanıcı adı girin.');
      return false;
    }

    if (username.trim().length < 3) {
      Alert.alert('Geçersiz Kullanıcı Adı', 'Kullanıcı adı en az 3 karakter olmalıdır.');
      return false;
    }

    if (!/^[a-zA-Z0-9_]+$/.test(username.trim())) {
      Alert.alert('Geçersiz Kullanıcı Adı', 'Kullanıcı adı sadece harf, rakam ve alt çizgi içerebilir.');
      return false;
    }

    if (selectedPreferences.length === 0) {
      Alert.alert('Eksik Bilgi', 'Lütfen en az bir kahve tercihi seçin.');
      return false;
    }

    if (selectedPurposeIds.length === 0) {
      Alert.alert('Eksik Bilgi', 'Lütfen en az bir kullanım amacı seçin.');
      return false;
    }

    return true;
  };

  const getOnboardingErrorMessage = (error) => {
    if (error.code === '23505') {
      return 'Bu kullanıcı adı zaten alınmış. Lütfen başka bir kullanıcı adı deneyin.';
    }
    if (error.code === 'PGRST301') {
      return 'Profil oluşturulamadı. Lütfen tekrar giriş yapın.';
    }
    return `Profil güncellenirken hata oluştu: ${error.message}`;
  };

  const handleComplete = async () => {
    if (!validateOnboardingData()) {
      return;
    }

    setLoading(true);
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError) {
        console.error('User fetch error:', userError);
        Alert.alert('Oturum Hatası', 'Kullanıcı oturumu doğrulanamadı. Lütfen tekrar giriş yapın.');
        return;
      }

      if (!user) {
        Alert.alert('Oturum Hatası', 'Kullanıcı oturumu bulunamadı. Lütfen tekrar giriş yapın.');
        return;
      }

      console.log('Updating profile for user:', user.id);

      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          username: username.trim().toLowerCase(),
          coffee_preferences: selectedPreferences,
          usage_purpose: selectedPurposeIds,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (updateError) {
        console.error('Profile update error:', updateError);
        Alert.alert('Profil Güncellenemedi', getOnboardingErrorMessage(updateError));
        return;
      }

      console.log('Profile updated successfully');
      
      // Show completion animation
      setShowCompletion(true);
      
      // Wait for animation, then navigate
      setTimeout(async () => {
        // Trigger auth state change to refresh profile in AppNavigator
        const { data: { session } } = await supabase.auth.getSession();
        if (session) {
          await supabase.auth.setSession(session);
        }
      }, 2000); // 2 saniye animasyon süresi
      
    } catch (error) {
      console.error('Unexpected onboarding error:', error);
      Alert.alert('Beklenmeyen Hata', 'Bir hata oluştu. Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  if (showCompletion) {
    return <CompletionAnimation />;
  }

  return (
    <View style={styles.container}>
      {/* Progress Indicator */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${((currentStep + 1) / 3) * 100}%` }]} />
        </View>
        <Text style={[styles.progressText, globalStyles.bodySmall]}>
          {currentStep + 1} / 3
        </Text>
      </View>

      {/* Animated Coffee Cup */}
      <View style={styles.cupContainer}>
        <AnimatedCoffeeCup 
          step={currentStep}
          selectedPreferences={selectedPreferences}
          isCompleted={false}
        />
      </View>

      {/* Horizontal Scrollable Steps */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        scrollEnabled={false}
        style={styles.stepsContainer}
      >
        {/* Step 1: Username */}
        <View style={styles.stepContainer}>
          <View style={styles.stepContent}>
            <Text style={[styles.stepTitle, globalStyles.heading]}>
              Merhaba! 👋
            </Text>
            <Text style={[styles.stepSubtitle, globalStyles.body]}>
              Seni nasıl çağıralım?
            </Text>
            
            <TextInput
              style={[styles.input, globalStyles.body]}
              placeholder="Kullanıcı adın"
              placeholderTextColor={Colors.tabIconDefault}
              value={username}
              onChangeText={setUsername}
              autoCapitalize="none"
              autoFocus={currentStep === 0}
            />
            
            {username.trim() && (
              <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
                <Text style={[styles.nextButtonText, globalStyles.body]}>
                  Devam Et
                </Text>
                <Ionicons name="arrow-forward" size={20} color={Colors.text} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Step 2: Coffee Preferences */}
        <View style={styles.stepContainer}>
          <View style={styles.stepContent}>
            <Text style={[styles.stepTitle, globalStyles.heading]}>
              Kahve Zevkin 🌟
            </Text>
            <Text style={[styles.stepSubtitle, globalStyles.body]}>
              Hangi tatları seviyorsun?
            </Text>
            
            <View style={styles.tagsContainer}>
              {COFFEE_PREFERENCES.map((preference) => (
                <TouchableOpacity
                  key={preference}
                  style={[
                    styles.tag,
                    selectedPreferences.includes(preference) && styles.tagSelected
                  ]}
                  onPress={() => togglePreference(preference)}
                >
                  <Text style={[
                    styles.tagText,
                    globalStyles.bodySmall,
                    selectedPreferences.includes(preference) && styles.tagTextSelected
                  ]}>
                    {preference}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            
            <View style={styles.navigationButtons}>
              <TouchableOpacity style={styles.backButton} onPress={goToPrevStep}>
                <Ionicons name="arrow-back" size={20} color={Colors.tabIconDefault} />
                <Text style={[styles.backButtonText, globalStyles.bodySmall]}>
                  Geri
                </Text>
              </TouchableOpacity>
              
              {selectedPreferences.length > 0 && (
                <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
                  <Text style={[styles.nextButtonText, globalStyles.body]}>
                    Devam Et
                  </Text>
                  <Ionicons name="arrow-forward" size={20} color={Colors.text} />
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>

        {/* Step 3: Usage Purpose */}
        <View style={styles.stepContainer}>
          <View style={styles.stepContent}>
            <Text style={[styles.stepTitle, globalStyles.heading]}>
              Kullanım Amacın 🎯
            </Text>
            <Text style={[styles.stepSubtitle, globalStyles.body]}>
              Uygulamayı ne için kullanacaksın?
            </Text>

            <View style={styles.cardsContainer}>
              {USAGE_PURPOSES.map((purpose) => (
                <TouchableOpacity
                  key={purpose.id}
                  style={[
                    styles.purposeCard,
                    selectedPurposeIds.includes(purpose.id) && styles.purposeCardSelected
                  ]}
                  onPress={() => togglePurpose(purpose.id)}
                >
                  <Text style={styles.purposeIcon}>{purpose.icon}</Text>
                  <Text style={[
                    styles.purposeText,
                    globalStyles.bodySmall,
                    selectedPurposeIds.includes(purpose.id) && styles.purposeTextSelected
                  ]}>
                    {purpose.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.navigationButtons}>
              <TouchableOpacity style={styles.backButton} onPress={goToPrevStep}>
                <Ionicons name="arrow-back" size={20} color={Colors.tabIconDefault} />
                <Text style={[styles.backButtonText, globalStyles.bodySmall]}>
                  Geri
                </Text>
              </TouchableOpacity>

              {selectedPurposeIds.length > 0 && (
                <TouchableOpacity
                  style={[styles.completeButton, loading && styles.buttonDisabled]}
                  onPress={handleComplete}
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <ActivityIndicator color={Colors.text} size="small" />
                      <Text style={[styles.completeButtonText, globalStyles.body, { marginLeft: 8 }]}>
                        Yolculuk Başlıyor...
                      </Text>
                    </>
                  ) : (
                    <>
                      <Text style={[styles.completeButtonText, globalStyles.body]}>
                        Yolculuğu Tamamla
                      </Text>
                      <Ionicons name="rocket" size={20} color={Colors.text} />
                    </>
                  )}
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: Colors.card,
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 2,
  },
  progressText: {
    color: Colors.tabIconDefault,
  },
  cupContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  stepsContainer: {
    flex: 1,
  },
  stepContainer: {
    width: SCREEN_WIDTH,
    flex: 1,
    paddingHorizontal: 20,
  },
  stepContent: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 40,
  },
  stepTitle: {
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 12,
  },
  stepSubtitle: {
    color: Colors.tabIconDefault,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 24,
  },
  input: {
    backgroundColor: Colors.card,
    color: Colors.text,
    borderRadius: 16,
    paddingHorizontal: 20,
    paddingVertical: 18,
    marginBottom: 30,
    fontSize: 16,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 40,
  },
  tag: {
    backgroundColor: Colors.card,
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  tagSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
    transform: [{ scale: 1.05 }],
  },
  tagText: {
    color: Colors.text,
    fontWeight: '500',
  },
  tagTextSelected: {
    color: Colors.background,
    fontWeight: '600',
  },
  cardsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 40,
  },
  purposeCard: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    width: '47%',
    borderWidth: 2,
    borderColor: 'transparent',
    minHeight: 100,
    justifyContent: 'center',
  },
  purposeCardSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
    transform: [{ scale: 1.02 }],
  },
  purposeIcon: {
    fontSize: 32,
    marginBottom: 12,
  },
  purposeText: {
    color: Colors.text,
    textAlign: 'center',
    fontWeight: '500',
  },
  purposeTextSelected: {
    color: Colors.background,
    fontWeight: '600',
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    gap: 8,
  },
  backButtonText: {
    color: Colors.tabIconDefault,
  },
  nextButton: {
    backgroundColor: Colors.primary,
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  nextButtonText: {
    color: Colors.text,
    fontWeight: '600',
    fontSize: 16,
  },
  completeButton: {
    backgroundColor: Colors.primary,
    borderRadius: 16,
    paddingVertical: 18,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 8,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 12,
  },
  completeButtonText: {
    color: Colors.text,
    fontWeight: '700',
    fontSize: 16,
  },
  buttonDisabled: {
    opacity: 0.7,
    transform: [{ scale: 0.98 }],
  },
});
