import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  interpolate
} from 'react-native-reanimated';
import Svg, { Circle, Line, Polygon, Text as SvgText, G } from 'react-native-svg';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';

const AnimatedPolygon = Animated.createAnimatedComponent(Polygon);

export default function TasteRadarChart({ coffeePreferences = [], size = 200 }) {
  const animationProgress = useSharedValue(0);
  
  // Kahve özelliklerini skorlara çevir
  const getScoreFromPreferences = () => {
    const scores = {
      acidity: 0,
      body: 0,
      sweetness: 0,
      bitterness: 0,
      aroma: 0
    };

    coffeePreferences.forEach(pref => {
      switch (pref) {
        case 'Meyvemsi':
          scores.acidity += 0.8;
          scores.sweetness += 0.6;
          break;
        case 'Çikolatamsı':
          scores.body += 0.9;
          scores.sweetness += 0.7;
          scores.bitterness += 0.4;
          break;
        case 'Fındıklı':
          scores.body += 0.6;
          scores.sweetness += 0.8;
          scores.aroma += 0.7;
          break;
        case 'Karamelli':
          scores.sweetness += 0.9;
          scores.body += 0.5;
          break;
        case 'Çiçeksi':
          scores.aroma += 0.9;
          scores.acidity += 0.6;
          break;
        case 'Baharatlı':
          scores.aroma += 0.8;
          scores.bitterness += 0.6;
          break;
        case 'Koyu Kavrum':
          scores.bitterness += 0.9;
          scores.body += 0.8;
          break;
        case 'Açık Kavrum':
          scores.acidity += 0.9;
          scores.aroma += 0.6;
          break;
      }
    });

    // Normalize scores (0-1 range)
    Object.keys(scores).forEach(key => {
      scores[key] = Math.min(scores[key], 1);
    });

    return scores;
  };

  const scores = getScoreFromPreferences();
  const center = size / 2;
  const radius = size * 0.35;
  
  // 5 köşeli radar chart için açılar
  const angles = [0, 72, 144, 216, 288].map(deg => (deg * Math.PI) / 180);
  const labels = ['Asidite', 'Gövde', 'Tatlılık', 'Acılık', 'Aroma'];
  const scoreValues = [scores.acidity, scores.body, scores.sweetness, scores.bitterness, scores.aroma];

  // Radar chart noktalarını hesapla
  const getPoints = (values, animProgress = 1) => {
    return values.map((value, index) => {
      const angle = angles[index];
      const distance = radius * value * animProgress;
      const x = center + distance * Math.cos(angle - Math.PI / 2);
      const y = center + distance * Math.sin(angle - Math.PI / 2);
      return `${x},${y}`;
    }).join(' ');
  };

  // Grid çizgileri için noktalar
  const getGridPoints = (level) => {
    return angles.map(angle => {
      const distance = radius * level;
      const x = center + distance * Math.cos(angle - Math.PI / 2);
      const y = center + distance * Math.sin(angle - Math.PI / 2);
      return `${x},${y}`;
    }).join(' ');
  };

  // Label pozisyonları
  const getLabelPosition = (index) => {
    const angle = angles[index];
    const distance = radius * 1.2;
    const x = center + distance * Math.cos(angle - Math.PI / 2);
    const y = center + distance * Math.sin(angle - Math.PI / 2);
    return { x, y };
  };

  useEffect(() => {
    animationProgress.value = withDelay(300, withTiming(1, { duration: 1500 }));
  }, [coffeePreferences]);

  const animatedStyle = useAnimatedStyle(() => {
    const progress = animationProgress.value;
    return {
      opacity: interpolate(progress, [0, 1], [0, 1])
    };
  });

  return (
    <View style={styles.container}>
      <Text style={[styles.title, globalStyles.subheading]}>Tat Profili</Text>
      
      <Animated.View style={[styles.chartContainer, animatedStyle]}>
        <Svg width={size} height={size}>
          {/* Grid çizgileri */}
          {[0.2, 0.4, 0.6, 0.8, 1].map((level, index) => (
            <Polygon
              key={`grid-${index}`}
              points={getGridPoints(level)}
              fill="none"
              stroke={Colors.tabIconDefault}
              strokeWidth="1"
              opacity={0.3}
            />
          ))}
          
          {/* Merkez çizgileri */}
          {angles.map((angle, index) => {
            const x = center + radius * Math.cos(angle - Math.PI / 2);
            const y = center + radius * Math.sin(angle - Math.PI / 2);
            return (
              <Line
                key={`axis-${index}`}
                x1={center}
                y1={center}
                x2={x}
                y2={y}
                stroke={Colors.tabIconDefault}
                strokeWidth="1"
                opacity={0.5}
              />
            );
          })}
          
          {/* Ana veri poligonu */}
          <AnimatedPolygon
            points={getPoints(scoreValues, animationProgress.value)}
            fill={Colors.primary}
            fillOpacity={0.3}
            stroke={Colors.primary}
            strokeWidth="2"
          />
          
          {/* Veri noktaları */}
          {scoreValues.map((value, index) => {
            const angle = angles[index];
            const distance = radius * value;
            const x = center + distance * Math.cos(angle - Math.PI / 2);
            const y = center + distance * Math.sin(angle - Math.PI / 2);
            
            return (
              <Circle
                key={`point-${index}`}
                cx={x}
                cy={y}
                r="4"
                fill={Colors.primary}
                stroke={Colors.background}
                strokeWidth="2"
              />
            );
          })}
        </Svg>
        
        {/* Etiketler */}
        {labels.map((label, index) => {
          const position = getLabelPosition(index);
          return (
            <View
              key={`label-${index}`}
              style={[
                styles.label,
                {
                  left: position.x - 25,
                  top: position.y - 10
                }
              ]}
            >
              <Text style={[styles.labelText, globalStyles.bodySmall]}>
                {label}
              </Text>
            </View>
          );
        })}
      </Animated.View>
      
      {/* Skor göstergeleri */}
      <View style={styles.scoresContainer}>
        {labels.map((label, index) => (
          <View key={`score-${index}`} style={styles.scoreItem}>
            <Text style={[styles.scoreLabel, globalStyles.bodySmall]}>
              {label}
            </Text>
            <View style={styles.scoreBar}>
              <View 
                style={[
                  styles.scoreBarFill, 
                  { width: `${scoreValues[index] * 100}%` }
                ]} 
              />
            </View>
            <Text style={[styles.scoreValue, globalStyles.bodySmall]}>
              {Math.round(scoreValues[index] * 100)}%
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: 20,
  },
  title: {
    color: Colors.text,
    marginBottom: 20,
    textAlign: 'center',
  },
  chartContainer: {
    position: 'relative',
    marginBottom: 30,
  },
  label: {
    position: 'absolute',
    width: 50,
    alignItems: 'center',
  },
  labelText: {
    color: Colors.text,
    textAlign: 'center',
    fontWeight: '600',
  },
  scoresContainer: {
    width: '100%',
    gap: 12,
  },
  scoreItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  scoreLabel: {
    color: Colors.text,
    width: 60,
    fontWeight: '500',
  },
  scoreBar: {
    flex: 1,
    height: 6,
    backgroundColor: Colors.card,
    borderRadius: 3,
    overflow: 'hidden',
  },
  scoreBarFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 3,
  },
  scoreValue: {
    color: Colors.tabIconDefault,
    width: 35,
    textAlign: 'right',
    fontWeight: '600',
  },
});
