// Bu dosyayı terminal'de node clear_session.js komutu ile çalıştırabilirsiniz
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://xnkttsqnfduehouaggzr.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inhua3R0c3FuZmR1ZWhvdWFnZ3pyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNDYyMTksImV4cCI6MjA2NDcyMjIxOX0.DPLivfklW-QfHh-zVHaezZgm2Dh1fDk8XBBQNy_4h7s';

const supabase = createClient(supabaseUrl, supabaseKey);

async function clearSession() {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Çıkış yaparken hata:', error.message);
    } else {
      console.log('Oturum başarıyla kapatıldı!');
    }
  } catch (error) {
    console.error('Hata:', error);
  }
}

clearSession();