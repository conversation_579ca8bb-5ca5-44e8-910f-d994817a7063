// Tüm session'ları temizlemek için bu scripti çalıştırın
// node clear_all_sessions.js

import { createClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from './src/lib/supabase.js';

// .env dosyasından değerleri al
const supabaseUrl = process.env.SUPABASE_URL || 'https://xnkttsqnfduehouaggzr.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inhua3R0c3FuZmR1ZWhvdWFnZ3pyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNDYyMTksImV4cCI6MjA2NDcyMjIxOX0.DPLivfklW-QfHh-zVHaezZgm2Dh1fDk8XBBQNy_4h7s';

const supabase = createClient(supabaseUrl, supabaseKey);

async function clearAllSessions() {
  try {
    console.log('🧹 Tüm session\'ları temizleniyor...');
    
    // Mevcut session'ı temizle
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Session temizleme hatası:', error.message);
    } else {
      console.log('✅ Local session temizlendi!');
    }
    
    console.log('🎉 Uygulama sıfırlandı! Artık yeni kullanıcı kaydı yapabilirsiniz.');
    console.log('📱 Uygulamayı yeniden başlatın: npm start');
    
  } catch (error) {
    console.error('❌ Hata:', error);
  }
}

clearAllSessions();
