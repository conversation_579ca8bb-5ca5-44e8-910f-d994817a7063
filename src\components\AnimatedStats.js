import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedProps,
  withTiming,
  withDelay,
  interpolate,
  runOnJS
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';

const AnimatedText = Animated.createAnimatedComponent(Text);

// Sayı animasyonu için custom hook
function useCountingAnimation(targetValue, duration = 1500, delay = 0) {
  const animatedValue = useSharedValue(0);
  const displayValue = useSharedValue(0);

  useEffect(() => {
    animatedValue.value = withDelay(
      delay,
      withTiming(targetValue, { duration }, (finished) => {
        if (finished) {
          runOnJS(() => {
            displayValue.value = targetValue;
          })();
        }
      })
    );
  }, [targetValue, duration, delay]);

  const animatedProps = useAnimatedProps(() => {
    const currentValue = Math.floor(animatedValue.value);
    return {
      text: currentValue.toString(),
    } as any;
  });

  return animatedProps;
}

// Tek bir istatistik kartı
function StatCard({ icon, label, value, color = Colors.primary, delay = 0 }) {
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  
  const countingProps = useCountingAnimation(value, 1500, delay + 300);

  useEffect(() => {
    scale.value = withDelay(delay, withTiming(1, { duration: 600 }));
    opacity.value = withDelay(delay, withTiming(1, { duration: 400 }));
  }, [delay]);

  const cardStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={[styles.statCard, cardStyle]}>
      <View style={[styles.iconContainer, { backgroundColor: color + '20' }]}>
        <Ionicons name={icon} size={24} color={color} />
      </View>
      <View style={styles.statContent}>
        <AnimatedText
          style={[styles.statValue, globalStyles.heading, { color }]}
          animatedProps={countingProps}
        />
        <Text style={[styles.statLabel, globalStyles.bodySmall]}>
          {label}
        </Text>
      </View>
    </Animated.View>
  );
}

// Rozet komponenti
function Badge({ icon, label, isEarned, delay = 0 }) {
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  const rotation = useSharedValue(0);

  useEffect(() => {
    scale.value = withDelay(delay, withTiming(1, { duration: 600 }));
    opacity.value = withDelay(delay, withTiming(1, { duration: 400 }));
    
    if (isEarned) {
      rotation.value = withDelay(
        delay + 200,
        withTiming(360, { duration: 800 })
      );
    }
  }, [delay, isEarned]);

  const badgeStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { rotate: `${rotation.value}deg` }
    ],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={[styles.badge, badgeStyle]}>
      <View style={[
        styles.badgeIcon,
        {
          backgroundColor: isEarned ? Colors.primary : Colors.card,
          opacity: isEarned ? 1 : 0.5
        }
      ]}>
        <Ionicons 
          name={icon} 
          size={20} 
          color={isEarned ? Colors.background : Colors.tabIconDefault} 
        />
      </View>
      <Text style={[
        styles.badgeLabel,
        globalStyles.bodySmall,
        { color: isEarned ? Colors.text : Colors.tabIconDefault }
      ]}>
        {label}
      </Text>
    </Animated.View>
  );
}

export default function AnimatedStats({ 
  reviewCount = 0, 
  visitedCities = 0, 
  favoriteVenues = 0,
  totalVisits = 0 
}) {
  // Rozetlerin kazanılma durumları
  const badges = [
    {
      icon: 'chatbubble',
      label: 'İlk Yorum',
      isEarned: reviewCount >= 1
    },
    {
      icon: 'star',
      label: 'Yorum Ustası',
      isEarned: reviewCount >= 10
    },
    {
      icon: 'location',
      label: 'Gezgin',
      isEarned: visitedCities >= 3
    },
    {
      icon: 'heart',
      label: 'Kahve Aşığı',
      isEarned: favoriteVenues >= 5
    },
    {
      icon: 'trophy',
      label: 'Keşifçi',
      isEarned: totalVisits >= 20
    },
    {
      icon: 'diamond',
      label: 'Uzman',
      isEarned: reviewCount >= 50
    }
  ];

  return (
    <View style={styles.container}>
      {/* İstatistikler */}
      <View style={styles.statsContainer}>
        <StatCard
          icon="chatbubble-ellipses"
          label="Yorumlar"
          value={reviewCount}
          color="#FF6B6B"
          delay={0}
        />
        <StatCard
          icon="location"
          label="Şehirler"
          value={visitedCities}
          color="#4ECDC4"
          delay={200}
        />
        <StatCard
          icon="heart"
          label="Favoriler"
          value={favoriteVenues}
          color="#45B7D1"
          delay={400}
        />
        <StatCard
          icon="cafe"
          label="Ziyaretler"
          value={totalVisits}
          color="#F7DC6F"
          delay={600}
        />
      </View>

      {/* Rozetler */}
      <View style={styles.badgesSection}>
        <Text style={[styles.sectionTitle, globalStyles.subheading]}>
          Rozetlerim
        </Text>
        <View style={styles.badgesContainer}>
          {badges.map((badge, index) => (
            <Badge
              key={badge.label}
              icon={badge.icon}
              label={badge.label}
              isEarned={badge.isEarned}
              delay={800 + index * 100}
            />
          ))}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 30,
  },
  statCard: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    width: '47%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  statContent: {
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 2,
  },
  statLabel: {
    color: Colors.tabIconDefault,
    fontWeight: '500',
  },
  badgesSection: {
    marginTop: 20,
  },
  sectionTitle: {
    color: Colors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  badgesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    justifyContent: 'center',
  },
  badge: {
    alignItems: 'center',
    width: 80,
  },
  badgeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  badgeLabel: {
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: 16,
  },
});
