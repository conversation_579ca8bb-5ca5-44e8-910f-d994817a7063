import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';

export default function ProfileScreen() {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [signingOut, setSigningOut] = useState(false);

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (!error && data) {
          setProfile({ ...data, email: user.email });
        }
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    Alert.alert(
      'Çıkış Yap',
      'Hesabınızdan çıkmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Çıkış Yap',
          style: 'destructive',
          onPress: async () => {
            setSigningOut(true);
            try {
              const { error } = await supabase.auth.signOut();
              if (error) {
                console.error('Sign out error:', error);
                Alert.alert(
                  'Çıkış Hatası',
                  'Çıkış yaparken bir hata oluştu. Lütfen tekrar deneyin.',
                  [{ text: 'Tamam' }]
                );
              }
              // Başarılı çıkış durumunda navigation otomatik olarak auth listener tarafından yapılacak
            } catch (error) {
              console.error('Unexpected sign out error:', error);
              Alert.alert(
                'Beklenmeyen Hata',
                'Bir hata oluştu. Lütfen uygulamayı yeniden başlatın.',
                [{ text: 'Tamam' }]
              );
            } finally {
              setSigningOut(false);
            }
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={styles.centered}>
        <Text style={[styles.errorText, globalStyles.body]}>Profil yüklenemedi.</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          <Ionicons name="person-circle" size={80} color={Colors.primary} />
        </View>
        <Text style={[styles.name, globalStyles.heading]}>
          {profile.full_name || profile.username || 'Kullanıcı'}
        </Text>
        <Text style={[styles.username, globalStyles.body]}>@{profile.username}</Text>
        <Text style={[styles.email, globalStyles.bodySmall]}>{profile.email}</Text>
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, globalStyles.subheading]}>Kahve Zevkleri</Text>
        <View style={styles.tagsContainer}>
          {profile.coffee_preferences && profile.coffee_preferences.length > 0 ? (
            profile.coffee_preferences.map((preference, index) => (
              <View key={index} style={styles.tag}>
                <Text style={[styles.tagText, globalStyles.bodySmall]}>{preference}</Text>
              </View>
            ))
          ) : (
            <Text style={[styles.emptyText, globalStyles.bodySmall]}>
              Henüz kahve zevki belirtilmemiş
            </Text>
          )}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, globalStyles.subheading]}>Kullanım Amaçları</Text>
        <View style={styles.tagsContainer}>
          {profile.usage_purpose && profile.usage_purpose.length > 0 ? (
            profile.usage_purpose.map((purpose, index) => (
              <View key={index} style={styles.tag}>
                <Text style={[styles.tagText, globalStyles.bodySmall]}>{purpose}</Text>
              </View>
            ))
          ) : (
            <Text style={[styles.emptyText, globalStyles.bodySmall]}>
              Henüz kullanım amacı belirtilmemiş
            </Text>
          )}
        </View>
      </View>

      <View style={styles.section}>
        <TouchableOpacity
          style={[styles.signOutButton, signingOut && styles.buttonDisabled]}
          onPress={handleSignOut}
          disabled={signingOut}
        >
          {signingOut ? (
            <>
              <ActivityIndicator size="small" color={Colors.text} />
              <Text style={[styles.signOutText, globalStyles.body]}>Çıkış Yapılıyor...</Text>
            </>
          ) : (
            <>
              <Ionicons name="log-out-outline" size={20} color={Colors.text} />
              <Text style={[styles.signOutText, globalStyles.body]}>Çıkış Yap</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#444',
  },
  avatarContainer: {
    marginBottom: 16,
  },
  name: {
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 4,
  },
  username: {
    color: Colors.primary,
    textAlign: 'center',
    marginBottom: 4,
  },
  email: {
    color: Colors.tabIconDefault,
    textAlign: 'center',
  },
  section: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#444',
  },
  sectionTitle: {
    color: Colors.text,
    marginBottom: 16,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  tagText: {
    color: Colors.text,
  },
  emptyText: {
    color: Colors.tabIconDefault,
    fontStyle: 'italic',
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.card,
    borderRadius: 12,
    paddingVertical: 16,
    gap: 8,
  },
  signOutText: {
    color: Colors.text,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  errorText: {
    color: 'red',
  },
});