import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Alert, ImageBackground } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { supabase } from '../lib/supabase';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import { toast } from '../utils/toast';
import spacingScale from '../constants/spacing';
import AnimatedBlob from '../components/AnimatedBlob';
import StatCard from '../components/StatCard';
import AchievementBadge from '../components/AchievementBadge';
import TasteCard from '../components/TasteCard';

export default function ProfileScreen() {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [signingOut, setSigningOut] = useState(false);
  const [stats, setStats] = useState({
    reviewCount: 0,
    cityCount: 0,
    venueCount: 0,
  });
  const [achievements, setAchievements] = useState([]);

  useEffect(() => {
    fetchProfile();
    fetchStats();
    generateAchievements();
  }, []);

  const fetchStats = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Yorum sayısı
      const { count: reviewCount } = await supabase
        .from('reviews')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id);

      // Ziyaret edilen şehir sayısı (reviews üzerinden venues'e join)
      const { data: cityData } = await supabase
        .from('reviews')
        .select('venues(city)')
        .eq('user_id', user.id);

      const uniqueCities = [...new Set(cityData?.map(item => item.venues?.city).filter(Boolean))];

      // Ziyaret edilen mekan sayısı
      const { data: venueData } = await supabase
        .from('reviews')
        .select('venue_id')
        .eq('user_id', user.id);

      const uniqueVenues = [...new Set(venueData?.map(item => item.venue_id))];

      setStats({
        reviewCount: reviewCount || 0,
        cityCount: uniqueCities.length,
        venueCount: uniqueVenues.length,
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const generateAchievements = () => {
    const allAchievements = [
      {
        id: 'first_review',
        icon: 'chatbubble',
        title: 'İlk Yorum',
        description: 'İlk yorumunu yaptın!',
        condition: (stats) => stats.reviewCount >= 1,
      },
      {
        id: 'coffee_explorer',
        icon: 'compass',
        title: 'Kahve Kaşifi',
        description: '5 farklı mekan keşfettin',
        condition: (stats) => stats.venueCount >= 5,
      },
      {
        id: 'city_traveler',
        icon: 'airplane',
        title: 'Şehir Gezgini',
        description: '3 farklı şehir ziyaret ettin',
        condition: (stats) => stats.cityCount >= 3,
      },
      {
        id: 'review_master',
        icon: 'star',
        title: 'Yorum Ustası',
        description: '10 yorum yazdın',
        condition: (stats) => stats.reviewCount >= 10,
      },
    ];

    const earnedAchievements = allAchievements.map(achievement => ({
      ...achievement,
      earned: achievement.condition(stats),
    }));

    setAchievements(earnedAchievements);
  };

  useEffect(() => {
    if (stats.reviewCount > 0 || stats.cityCount > 0 || stats.venueCount > 0) {
      generateAchievements();
    }
  }, [stats]);

  const fetchProfile = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (!error && data) {
          setProfile({ ...data, email: user.email });
        }
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    Alert.alert(
      'Çıkış Yap',
      'Hesabınızdan çıkmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Çıkış Yap',
          style: 'destructive',
          onPress: async () => {
            setSigningOut(true);
            try {
              const { error } = await supabase.auth.signOut();
              if (error) {
                console.error('Sign out error:', error);
                toast.error('Çıkış Hatası', 'Çıkış yaparken bir hata oluştu. Lütfen tekrar deneyin.');
              } else {
                toast.success('Çıkış Yapıldı', 'Başarıyla çıkış yaptınız.');
              }
              // Başarılı çıkış durumunda navigation otomatik olarak auth listener tarafından yapılacak
            } catch (error) {
              console.error('Unexpected sign out error:', error);
              toast.error('Beklenmeyen Hata', 'Lütfen uygulamayı yeniden başlatın.');
            } finally {
              setSigningOut(false);
            }
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={styles.centered}>
        <Text style={[styles.errorText, globalStyles.body]}>Profil yüklenemedi.</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Animated Background */}
      <View style={styles.backgroundContainer}>
        <AnimatedBlob size={300} color={Colors.primary} />
        <AnimatedBlob size={200} color={Colors.secondary} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Hero Header */}
        <LinearGradient
          colors={[Colors.primary + '20', 'transparent']}
          style={styles.heroHeader}
        >
          <View style={styles.avatarSection}>
            <View style={styles.avatarContainer}>
              <Ionicons name="person-circle" size={100} color={Colors.primary} />
            </View>
            <Text style={[styles.name, globalStyles.heading]}>
              {profile.full_name || profile.username || 'Kullanıcı'}
            </Text>
            <Text style={[styles.username, globalStyles.body]}>@{profile.username}</Text>
          </View>
        </LinearGradient>

        {/* Stats Section */}
        <View style={styles.statsSection}>
          <Text style={[styles.sectionTitle, globalStyles.subheading]}>İstatistikler</Text>
          <StatCard
            icon="chatbubble-ellipses"
            value={stats.reviewCount}
            label="Toplam Yorum"
            delay={0}
          />
          <StatCard
            icon="location"
            value={stats.cityCount}
            label="Ziyaret Edilen Şehir"
            delay={200}
          />
          <StatCard
            icon="storefront"
            value={stats.venueCount}
            label="Keşfedilen Mekan"
            delay={400}
          />
        </View>

        {/* Achievements Section */}
        <View style={styles.achievementsSection}>
          <Text style={[styles.sectionTitle, globalStyles.subheading]}>Başarılar</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.achievementsScroll}>
            {achievements.map((achievement, index) => (
              <AchievementBadge
                key={achievement.id}
                icon={achievement.icon}
                title={achievement.title}
                description={achievement.description}
                earned={achievement.earned}
                index={index}
              />
            ))}
          </ScrollView>
        </View>

        {/* Coffee Preferences */}
        <View style={styles.tastesSection}>
          <Text style={[styles.sectionTitle, globalStyles.subheading]}>Kahve Zevkleri</Text>
          <View style={styles.tasteCardsContainer}>
            {profile.coffee_preferences && profile.coffee_preferences.length > 0 ? (
              profile.coffee_preferences.map((preference, index) => (
                <TasteCard
                  key={index}
                  taste={preference}
                  index={index}
                  type="preference"
                />
              ))
            ) : (
              <Text style={[styles.emptyText, globalStyles.bodySmall]}>
                Henüz kahve zevki belirtilmemiş
              </Text>
            )}
          </View>
        </View>

        {/* Usage Purposes */}
        <View style={styles.tastesSection}>
          <Text style={[styles.sectionTitle, globalStyles.subheading]}>Kullanım Amaçları</Text>
          <View style={styles.tasteCardsContainer}>
            {profile.usage_purpose && profile.usage_purpose.length > 0 ? (
              profile.usage_purpose.map((purpose, index) => (
                <TasteCard
                  key={index}
                  taste={purpose}
                  index={index}
                  type="purpose"
                />
              ))
            ) : (
              <Text style={[styles.emptyText, globalStyles.bodySmall]}>
                Henüz kullanım amacı belirtilmemiş
              </Text>
            )}
          </View>
        </View>

        {/* Sign Out Section */}
        <View style={styles.signOutSection}>
          <TouchableOpacity
            style={[styles.signOutButton, signingOut && styles.buttonDisabled]}
            onPress={handleSignOut}
            disabled={signingOut}
          >
            {signingOut ? (
              <>
                <ActivityIndicator size="small" color={Colors.text} />
                <Text style={[styles.signOutText, globalStyles.body]}>Çıkış Yapılıyor...</Text>
              </>
            ) : (
              <>
                <Ionicons name="log-out-outline" size={20} color={Colors.text} />
                <Text style={[styles.signOutText, globalStyles.body]}>Çıkış Yap</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
    zIndex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  heroHeader: {
    paddingTop: spacingScale.xxl,
    paddingBottom: spacingScale.xl,
    paddingHorizontal: spacingScale.lg,
  },
  avatarSection: {
    alignItems: 'center',
  },
  avatarContainer: {
    marginBottom: spacingScale.md,
    shadowColor: Colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  name: {
    color: Colors.text,
    textAlign: 'center',
    marginBottom: spacingScale.xs,
    textShadowColor: Colors.primary + '50',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  username: {
    color: Colors.primary,
    textAlign: 'center',
    marginBottom: spacingScale.sm,
  },
  statsSection: {
    paddingHorizontal: spacingScale.lg,
    paddingVertical: spacingScale.xl,
  },
  achievementsSection: {
    paddingVertical: spacingScale.xl,
  },
  achievementsScroll: {
    paddingHorizontal: spacingScale.lg,
  },
  tastesSection: {
    paddingHorizontal: spacingScale.lg,
    paddingVertical: spacingScale.xl,
  },
  sectionTitle: {
    color: Colors.text,
    marginBottom: spacingScale.lg,
    paddingHorizontal: spacingScale.sm,
  },
  tasteCardsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  emptyText: {
    color: Colors.tabIconDefault,
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: spacingScale.lg,
  },
  signOutSection: {
    paddingHorizontal: spacingScale.lg,
    paddingVertical: spacingScale.xl,
    paddingBottom: spacingScale.xxxl,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.card,
    borderRadius: 16,
    paddingVertical: spacingScale.lg,
    gap: spacingScale.sm,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: 1,
    borderColor: Colors.tabIconDefault + '30',
  },
  signOutText: {
    color: Colors.text,
    fontWeight: '500',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  errorText: {
    color: 'red',
  },
});