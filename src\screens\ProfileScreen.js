import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  Dimensions,
  ImageBackground
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedScrollHandler,
  interpolate,
  withTiming,
  withSpring,
  Extrapolate
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import TasteRadarChart from '../components/TasteRadarChart';
import AnimatedStats from '../components/AnimatedStats';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const HEADER_HEIGHT = 300;
const AVATAR_SIZE = 120;

export default function ProfileScreen() {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [signingOut, setSigningOut] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [stats, setStats] = useState({
    reviewCount: 0,
    visitedCities: 0,
    favoriteVenues: 0,
    totalVisits: 0
  });

  const scrollY = useSharedValue(0);
  const headerScale = useSharedValue(1);
  const avatarScale = useSharedValue(1);

  useEffect(() => {
    fetchProfile();
    fetchStats();
  }, []);

  const fetchProfile = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (!error && data) {
          setProfile({ ...data, email: user.email });
        }
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Review sayısını al
      const { data: reviews } = await supabase
        .from('reviews')
        .select('id, venue_id')
        .eq('user_id', user.id);

      // Benzersiz şehir sayısını hesapla (venues tablosundan)
      const { data: venues } = await supabase
        .from('venues')
        .select('city')
        .in('id', reviews?.map(r => r.venue_id) || []);

      const uniqueCities = [...new Set(venues?.map(v => v.city) || [])];

      setStats({
        reviewCount: reviews?.length || 0,
        visitedCities: uniqueCities.length,
        favoriteVenues: Math.min(reviews?.length || 0, 10), // Basit hesaplama
        totalVisits: (reviews?.length || 0) * 2 // Basit hesaplama
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  // Scroll handler for parallax effect
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  // Animated styles
  const headerStyle = useAnimatedStyle(() => {
    const translateY = interpolate(
      scrollY.value,
      [0, HEADER_HEIGHT],
      [0, -HEADER_HEIGHT / 2],
      Extrapolate.CLAMP
    );

    const scale = interpolate(
      scrollY.value,
      [0, HEADER_HEIGHT],
      [1, 0.8],
      Extrapolate.CLAMP
    );

    return {
      transform: [{ translateY }, { scale }],
    };
  });

  const avatarStyle = useAnimatedStyle(() => {
    const scale = interpolate(
      scrollY.value,
      [0, HEADER_HEIGHT / 2],
      [1, 0.6],
      Extrapolate.CLAMP
    );

    const translateY = interpolate(
      scrollY.value,
      [0, HEADER_HEIGHT],
      [0, -50],
      Extrapolate.CLAMP
    );

    return {
      transform: [{ scale }, { translateY }],
    };
  });

  const contentOpacity = useAnimatedStyle(() => {
    const opacity = interpolate(
      scrollY.value,
      [HEADER_HEIGHT - 100, HEADER_HEIGHT],
      [0, 1],
      Extrapolate.CLAMP
    );

    return { opacity };
  });

  const handleSignOut = async () => {
    Alert.alert(
      'Çıkış Yap',
      'Hesabınızdan çıkmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Çıkış Yap',
          style: 'destructive',
          onPress: async () => {
            setSigningOut(true);
            try {
              const { error } = await supabase.auth.signOut();
              if (error) {
                console.error('Sign out error:', error);
                Alert.alert(
                  'Çıkış Hatası',
                  'Çıkış yaparken bir hata oluştu. Lütfen tekrar deneyin.',
                  [{ text: 'Tamam' }]
                );
              }
              // Başarılı çıkış durumunda navigation otomatik olarak auth listener tarafından yapılacak
            } catch (error) {
              console.error('Unexpected sign out error:', error);
              Alert.alert(
                'Beklenmeyen Hata',
                'Bir hata oluştu. Lütfen uygulamayı yeniden başlatın.',
                [{ text: 'Tamam' }]
              );
            } finally {
              setSigningOut(false);
            }
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={styles.centered}>
        <Text style={[styles.errorText, globalStyles.body]}>Profil yüklenemedi.</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Parallax Header */}
      <Animated.View style={[styles.header, headerStyle]}>
        <View style={styles.headerBackground} />
        <Animated.View style={[styles.avatarContainer, avatarStyle]}>
          <View style={styles.avatar}>
            <Ionicons name="person-circle" size={AVATAR_SIZE} color={Colors.primary} />
          </View>
        </Animated.View>
        <Text style={[styles.name, globalStyles.heading]}>
          {profile.full_name || profile.username || 'Kullanıcı'}
        </Text>
        <Text style={[styles.username, globalStyles.body]}>@{profile.username}</Text>
      </Animated.View>

      {/* Scrollable Content */}
      <Animated.ScrollView
        style={styles.scrollContainer}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
      >
        {/* Content Spacer */}
        <View style={{ height: HEADER_HEIGHT - 100 }} />

        {/* Stats Section */}
        <Animated.View style={[styles.contentContainer, contentOpacity]}>
          <AnimatedStats
            reviewCount={stats.reviewCount}
            visitedCities={stats.visitedCities}
            favoriteVenues={stats.favoriteVenues}
            totalVisits={stats.totalVisits}
          />

          {/* Taste Profile Chart */}
          {profile.coffee_preferences && profile.coffee_preferences.length > 0 && (
            <View style={styles.chartSection}>
              <TasteRadarChart coffeePreferences={profile.coffee_preferences} />
            </View>
          )}

          {/* Coffee Preferences Card */}
          <View style={styles.card}>
            <View style={styles.cardHeader}>
              <Text style={[styles.cardTitle, globalStyles.subheading]}>
                Kahve Zevkleri
              </Text>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => setEditMode(!editMode)}
              >
                <Ionicons
                  name={editMode ? "checkmark" : "create"}
                  size={20}
                  color={Colors.primary}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.tagsContainer}>
              {profile.coffee_preferences && profile.coffee_preferences.length > 0 ? (
                profile.coffee_preferences.map((preference, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={[styles.tagText, globalStyles.bodySmall]}>
                      {preference}
                    </Text>
                  </View>
                ))
              ) : (
                <Text style={[styles.emptyText, globalStyles.bodySmall]}>
                  Henüz kahve zevki belirtilmemiş
                </Text>
              )}
            </View>
          </View>

          {/* Usage Purpose Card */}
          <View style={styles.card}>
            <View style={styles.cardHeader}>
              <Text style={[styles.cardTitle, globalStyles.subheading]}>
                Kullanım Amaçları
              </Text>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => setEditMode(!editMode)}
              >
                <Ionicons
                  name={editMode ? "checkmark" : "create"}
                  size={20}
                  color={Colors.primary}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.tagsContainer}>
              {profile.usage_purpose && profile.usage_purpose.length > 0 ? (
                profile.usage_purpose.map((purpose, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={[styles.tagText, globalStyles.bodySmall]}>
                      {typeof purpose === 'number' ? `Amaç ${purpose}` : purpose}
                    </Text>
                  </View>
                ))
              ) : (
                <Text style={[styles.emptyText, globalStyles.bodySmall]}>
                  Henüz kullanım amacı belirtilmemiş
                </Text>
              )}
            </View>
          </View>

          {/* Sign Out Button */}
          <View style={styles.signOutSection}>
            <TouchableOpacity
              style={[styles.signOutButton, signingOut && styles.buttonDisabled]}
              onPress={handleSignOut}
              disabled={signingOut}
            >
              {signingOut ? (
                <>
                  <ActivityIndicator size="small" color={Colors.text} />
                  <Text style={[styles.signOutText, globalStyles.body]}>
                    Çıkış Yapılıyor...
                  </Text>
                </>
              ) : (
                <>
                  <Ionicons name="log-out-outline" size={20} color={Colors.text} />
                  <Text style={[styles.signOutText, globalStyles.body]}>
                    Çıkış Yap
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </View>

          {/* Bottom Spacer */}
          <View style={{ height: 100 }} />
        </Animated.View>
      </Animated.ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: HEADER_HEIGHT,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.primary + '20',
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  name: {
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 4,
    fontWeight: '700',
  },
  username: {
    color: Colors.primary,
    textAlign: 'center',
    marginBottom: 4,
    fontWeight: '600',
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    backgroundColor: Colors.background,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingTop: 20,
  },
  chartSection: {
    backgroundColor: Colors.card,
    borderRadius: 20,
    marginHorizontal: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  card: {
    backgroundColor: Colors.card,
    borderRadius: 20,
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    color: Colors.text,
    fontWeight: '700',
  },
  editButton: {
    padding: 8,
    borderRadius: 12,
    backgroundColor: Colors.primary + '20',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  tag: {
    backgroundColor: Colors.primary + '20',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: Colors.primary + '40',
  },
  tagText: {
    color: Colors.primary,
    fontWeight: '600',
  },
  emptyText: {
    color: Colors.tabIconDefault,
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: 20,
  },
  signOutSection: {
    paddingHorizontal: 20,
    marginTop: 20,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF6B6B',
    borderRadius: 16,
    paddingVertical: 16,
    gap: 8,
    shadowColor: '#FF6B6B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  signOutText: {
    color: Colors.background,
    fontWeight: '600',
    fontSize: 16,
  },
  buttonDisabled: {
    opacity: 0.6,
    transform: [{ scale: 0.98 }],
  },
  errorText: {
    color: '#FF6B6B',
    textAlign: 'center',
  },
});