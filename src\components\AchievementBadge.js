import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import spacingScale from '../constants/spacing';

export default function AchievementBadge({ 
  icon, 
  title, 
  description, 
  earned = false, 
  index = 0 
}) {
  return (
    <Animated.View 
      entering={FadeInDown.delay(index * 200)}
      style={[
        styles.container,
        { opacity: earned ? 1 : 0.4 }
      ]}
    >
      <View style={[
        styles.iconContainer,
        { backgroundColor: earned ? Colors.primary : Colors.tabIconDefault }
      ]}>
        <Ionicons 
          name={icon} 
          size={24} 
          color={earned ? Colors.background : Colors.text} 
        />
      </View>
      <Text style={[styles.title, globalStyles.bodySmall]}>
        {title}
      </Text>
      <Text style={[styles.description, globalStyles.caption]}>
        {description}
      </Text>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginRight: spacingScale.md,
    width: 80,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacingScale.sm,
    shadowColor: Colors.primary,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    color: Colors.text,
    textAlign: 'center',
    marginBottom: spacingScale.xs,
  },
  description: {
    color: Colors.tabIconDefault,
    textAlign: 'center',
    lineHeight: 12,
  },
});
