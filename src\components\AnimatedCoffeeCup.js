import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  interpolate,
  interpolateColor,
  Easing
} from 'react-native-reanimated';
import Svg, { Circle, Path, G, Defs, LinearGradient, Stop } from 'react-native-svg';
import { Colors } from '../constants/Colors';

const AnimatedSvg = Animated.createAnimatedComponent(Svg);

export default function AnimatedCoffeeCup({ 
  step = 0, 
  selectedPreferences = [], 
  isCompleted = false 
}) {
  const fillLevel = useSharedValue(0);
  const cupRotation = useSharedValue(0);
  const steamOpacity = useSharedValue(0);
  const completionScale = useSharedValue(1);
  const sparkleOpacity = useSharedValue(0);

  // Kahve rengi tercihlere göre değişir
  const getCoffeeColor = () => {
    if (selectedPreferences.includes('Çikolatamsı')) return '#8B4513';
    if (selectedPreferences.includes('Meyvemsi')) return '#CD853F';
    if (selectedPreferences.includes('Fındıklı')) return '#D2691E';
    if (selectedPreferences.includes('Koyu Kavrum')) return '#654321';
    return '#A0522D'; // Default kahve rengi
  };

  useEffect(() => {
    // Adım 0: Boş bardak sallanıyor
    if (step === 0) {
      cupRotation.value = withRepeat(
        withSequence(
          withTiming(5, { duration: 1000, easing: Easing.inOut(Easing.sin) }),
          withTiming(-5, { duration: 1000, easing: Easing.inOut(Easing.sin) })
        ),
        -1,
        true
      );
      fillLevel.value = withTiming(0, { duration: 500 });
      steamOpacity.value = withTiming(0, { duration: 300 });
    }
    
    // Adım 1: Kahve tercihleri seçildikçe dolmaya başlar
    else if (step === 1) {
      const targetFill = Math.min(selectedPreferences.length / 8, 0.7); // Max %70
      fillLevel.value = withTiming(targetFill, { 
        duration: 800, 
        easing: Easing.out(Easing.cubic) 
      });
      
      // Seçim yapıldığında parlama efekti
      if (selectedPreferences.length > 0) {
        sparkleOpacity.value = withSequence(
          withTiming(1, { duration: 200 }),
          withTiming(0, { duration: 400 })
        );
      }
    }
    
    // Adım 2: Tam dolu ve buhar çıkıyor
    else if (step === 2) {
      fillLevel.value = withTiming(0.85, { duration: 600 });
      steamOpacity.value = withTiming(1, { duration: 800 });
      cupRotation.value = withTiming(0, { duration: 500 });
    }
    
    // Tamamlandı: Büyük animasyon
    if (isCompleted) {
      completionScale.value = withSequence(
        withTiming(1.2, { duration: 300 }),
        withTiming(1, { duration: 200 })
      );
    }
  }, [step, selectedPreferences.length, isCompleted]);

  const cupStyle = useAnimatedStyle(() => ({
    transform: [
      { rotate: `${cupRotation.value}deg` },
      { scale: completionScale.value }
    ]
  }));

  const steamStyle = useAnimatedStyle(() => ({
    opacity: steamOpacity.value
  }));

  const sparkleStyle = useAnimatedStyle(() => ({
    opacity: sparkleOpacity.value
  }));

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.cupContainer, cupStyle]}>
        <AnimatedSvg width="120" height="140" viewBox="0 0 120 140">
          <Defs>
            <LinearGradient id="coffeeGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <Stop offset="0%" stopColor={getCoffeeColor()} stopOpacity="1" />
              <Stop offset="100%" stopColor="#3E2723" stopOpacity="1" />
            </LinearGradient>
            <LinearGradient id="cupGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#FFFFFF" stopOpacity="1" />
              <Stop offset="100%" stopColor="#F5F5F5" stopOpacity="1" />
            </LinearGradient>
          </Defs>
          
          {/* Bardak gövdesi */}
          <Path
            d="M25 50 L25 110 Q25 120 35 120 L75 120 Q85 120 85 110 L85 50 Z"
            fill="url(#cupGradient)"
            stroke="#E0E0E0"
            strokeWidth="2"
          />
          
          {/* Kahve içeriği */}
          <Path
            d={`M27 52 L27 ${110 - (fillLevel.value * 58)} Q27 ${120 - (fillLevel.value * 58)} 35 ${120 - (fillLevel.value * 58)} L75 ${120 - (fillLevel.value * 58)} Q83 ${120 - (fillLevel.value * 58)} 83 ${110 - (fillLevel.value * 58)} L83 52 Z`}
            fill="url(#coffeeGradient)"
          />
          
          {/* Bardak kulpu */}
          <Path
            d="M85 70 Q95 70 95 80 Q95 90 85 90"
            fill="none"
            stroke="#E0E0E0"
            strokeWidth="3"
            strokeLinecap="round"
          />
          
          {/* Bardak ağzı */}
          <Path
            d="M25 50 Q55 45 85 50"
            fill="none"
            stroke="#E0E0E0"
            strokeWidth="2"
          />
        </AnimatedSvg>
      </Animated.View>

      {/* Buhar efekti */}
      <Animated.View style={[styles.steamContainer, steamStyle]}>
        {[...Array(3)].map((_, index) => (
          <SteamParticle key={index} delay={index * 200} />
        ))}
      </Animated.View>

      {/* Parlama efekti */}
      <Animated.View style={[styles.sparkleContainer, sparkleStyle]}>
        <Ionicons name="sparkles" size={30} color={Colors.primary} />
      </Animated.View>
    </View>
  );
}

// Buhar parçacığı komponenti
function SteamParticle({ delay = 0 }) {
  const translateY = useSharedValue(0);
  const opacity = useSharedValue(0);

  useEffect(() => {
    const animate = () => {
      translateY.value = 0;
      opacity.value = 0;
      
      setTimeout(() => {
        translateY.value = withTiming(-40, { duration: 2000 });
        opacity.value = withSequence(
          withTiming(0.7, { duration: 500 }),
          withTiming(0, { duration: 1500 })
        );
      }, delay);
    };

    animate();
    const interval = setInterval(animate, 3000);
    return () => clearInterval(interval);
  }, [delay]);

  const steamStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
    opacity: opacity.value
  }));

  return (
    <Animated.View style={[styles.steamParticle, steamStyle]}>
      <View style={styles.steam} />
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
    position: 'relative'
  },
  cupContainer: {
    alignItems: 'center',
    justifyContent: 'center'
  },
  steamContainer: {
    position: 'absolute',
    top: 20,
    flexDirection: 'row',
    gap: 8
  },
  steamParticle: {
    width: 6,
    height: 6
  },
  steam: {
    width: 6,
    height: 6,
    backgroundColor: '#E0E0E0',
    borderRadius: 3,
    opacity: 0.6
  },
  sparkleContainer: {
    position: 'absolute',
    top: 10,
    right: 10
  }
});
