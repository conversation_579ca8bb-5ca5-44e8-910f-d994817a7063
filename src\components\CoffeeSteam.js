import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  interpolate,
  withDelay,
} from 'react-native-reanimated';
import { Colors } from '../constants/Colors';

const SteamParticle = ({ delay = 0, size = 4 }) => {
  const animationValue = useSharedValue(0);

  useEffect(() => {
    animationValue.value = withDelay(
      delay,
      withRepeat(
        withTiming(1, { duration: 3000 }),
        -1,
        false
      )
    );
  }, [delay]);

  const animatedStyle = useAnimatedStyle(() => {
    const translateY = interpolate(
      animationValue.value,
      [0, 1],
      [0, -100]
    );
    
    const opacity = interpolate(
      animationValue.value,
      [0, 0.3, 0.7, 1],
      [0, 0.8, 0.4, 0]
    );
    
    const scale = interpolate(
      animationValue.value,
      [0, 0.5, 1],
      [0.5, 1, 1.5]
    );

    return {
      transform: [
        { translateY },
        { scale },
      ],
      opacity,
    };
  });

  return (
    <Animated.View
      style={[
        styles.particle,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
        },
        animatedStyle,
      ]}
    />
  );
};

export default function CoffeeSteam() {
  return (
    <View style={styles.container}>
      <SteamParticle delay={0} size={3} />
      <SteamParticle delay={500} size={4} />
      <SteamParticle delay={1000} size={3} />
      <SteamParticle delay={1500} size={5} />
      <SteamParticle delay={2000} size={3} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: '20%',
    left: '50%',
    width: 50,
    height: 100,
    marginLeft: -25,
    zIndex: -1,
  },
  particle: {
    position: 'absolute',
    backgroundColor: Colors.primary + '30',
    left: '50%',
    bottom: 0,
  },
});
